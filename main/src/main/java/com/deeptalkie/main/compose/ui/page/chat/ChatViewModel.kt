package com.deeptalkie.main.compose.ui.page.chat

import android.content.Context
import android.content.Intent
import android.util.Base64
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.toRoute
import com.clevguard.utils.ext.logd
import com.clevguard.utils.ext.loge
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.Api
import com.deeptalkie.kidsguard.net.asyncError
import com.deeptalkie.kidsguard.net.asyncSuccess
import com.deeptalkie.main.App
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.ReportActivity
import com.deeptalkie.main.api.DeepTalkieApi
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.page.main.chats.ChatsRepo
import com.deeptalkie.main.compose.ui.page.main.video.playUrl
import com.deeptalkie.main.db.result.AIRoleSession
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.stateInViewModelDefault
import com.deeptalkie.main.repo.AIRoleRepo
import com.deeptalkie.main.view.RequirePurchaseDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.io.IOException
import okio.Path.Companion.toPath
import java.io.File
import java.io.FileOutputStream

class ChatViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    private val route = savedStateHandle.toRoute<MainRoute.Chat>()
    private val chatsRepo = ChatsRepo()
    private val msgRecordRepo = MsgRecordRepo()
    private val aiRoleRepo = AIRoleRepo()

    var aiRole by mutableStateOf<AIRoleSession?>(null)
        private set

    val messagesStateFlow = msgRecordRepo.queryCurrentSessionMsg(route.roleId)
        .stateInViewModelDefault(emptyList())

    var inputText by mutableStateOf("")
        private set

    var longPressedMsg by mutableStateOf<MsgRecord?>(null)
        private set

    var selectedVideoMsg by mutableStateOf<MsgRecord?>(null)
        private set

    var selectedReplyMsg by mutableStateOf<MsgRecord?>(null)
        private set

    var aiReplyState by mutableStateOf(AiReplyState())
        private set

    private val player by lazy {
        ExoPlayer.Builder(App.getInstance()).build()
    }

    var isVip by mutableStateOf(Membership.isVip())
        private set

    var hasMoreMsg = true
        private set

    var lastMessage by mutableStateOf<MsgRecord?>(null)
        private set

    var showWaitingMessage by mutableStateOf(false)
        private set

    private var playingVoiceMsgRecord by mutableStateOf<MsgRecord?>(null)

    init {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            msgRecordRepo.queryAIRoleSession(userId, route.roleId)
                .distinctUntilChanged()
                .collect {
                    aiRole = it
                }
        }

        viewModelScope.launch {
            messagesStateFlow.collect { msgRecords ->
                logv("消息列表数据刷新")
                val newLastMessage = msgRecords.firstOrNull()?.msg

                if (newLastMessage?.id == lastMessage?.id || newLastMessage?.msgId == lastMessage?.msgId) {
                    return@collect
                }

                lastMessage = newLastMessage

                viewModelScope.launch {
                    if (msgRecords.any { !it.msg.isRead }) {
                        logv("标记消息已读")
                        val userId = Membership.getUserId() ?: return@launch
                        msgRecordRepo.setRoleMsgIsRead(userId, route.roleId)
                    }
                }

                if (showWaitingMessage && msgRecords.firstOrNull()?.msg?.fromSelf == false) {
                    showWaitingMessage = false
                }
            }
        }

        viewModelScope.launch {
            Membership.userStateFlow.collect { userInfo ->
                userInfo ?: return@collect
                isVip = Membership.isVip()

                if (isVip && aiReplyState.enabled) {
                    requestAITips(App.getInstance()) {}
                }
            }
        }

        viewModelScope.launch {
            player.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    super.onIsPlayingChanged(isPlaying)
                    val mediaId = player.currentMediaItem?.mediaId
                    if (!isPlaying) {
                        if (mediaId == playingVoiceMsgRecord?.voicePath) {
                            playingVoiceMsgRecord = null
                        }
                    } else {
                        playingVoiceMsgRecord =
                            messagesStateFlow.value.find { it.msg.voicePath == mediaId }?.msg
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    super.onPlayerError(error)
                    playingVoiceMsgRecord = null
                }
            })
        }

        UserManager.onChatEnterEvent()
    }

    fun refreshMessages() {
        val userId = Membership.getUserId() ?: return
        viewModelScope.launch {
            val lastMsgId = messagesStateFlow.value.firstOrNull()?.msg?.msgId
            hasMoreMsg =
                chatsRepo.requestChatMessages(userId, route.roleId, route.sessionId, lastMsgId)
        }

        viewModelScope.launch {
            aiRoleRepo.requestIsFavorite(userId, route.roleId)
        }
    }

    fun onTextInput(text: String) {
        inputText = text
    }

    fun requestAITips(context: Context, gotoBuy: () -> Unit) {
        viewModelScope.launch {
            if (!aiReplyState.enabled) {
                expandAiReply()
                if (!aiReplyState.isUsed) {
                    return@launch
                }
            }
            if (!isCanAIReplyState(context, gotoBuy)) return@launch

            msgRecordRepo.getInspirations(route.sessionId, getAIReplyReqContent())
                ?.asyncSuccess { data ->
                    if (data?.messages.isNullOrEmpty()) {
                        withContext(Dispatchers.Main) {
                            showToast(App.getInstance().getString(R.string.not_network))
                        }
                        aiReplyState =
                            aiReplyState.copy(enabled = false, messages = listOf(), isUsed = true)
                        return@asyncSuccess
                    }
                    aiReplyState = aiReplyState.copy(messages = data.messages, isUsed = false)
                }
                ?.asyncError { _, _ ->
                    withContext(Dispatchers.Main) {
                        showToast(App.getInstance().getString(R.string.not_network))
                    }
                    aiReplyState =
                        aiReplyState.copy(enabled = false, messages = listOf(), isUsed = true)
                }
        }
    }

    fun foldAiReply() {
        aiReplyState = aiReplyState.copy(enabled = false)
    }

    fun expandAiReply() {
        aiReplyState = aiReplyState.copy(enabled = true)
    }

    private fun isCanAIReplyState(context: Context, gotoBuy: () -> Unit): Boolean {
        aiReplyState = aiReplyState.copy(
            enabled = true,
            messages = listOf(null, null, null)
        )
        if (!isVip) {
            aiReplyState = aiReplyState.copy(
                aiReplyLastCount = aiReplyState.aiReplyLastCount - 1,
            )
            if (aiReplyState.aiReplyLastCount < 0) {
                RequirePurchaseDialog(
                    context,
                    RequirePurchaseDialog.TYPE_INSPIRATION,
                    gotoBuy
                ).show()
                return false
            }
            UserManager.plusAIReplyCount()
        }
        return true
    }

    private fun getAIReplyReqContent(): String {
        val replyMsg = selectedReplyMsg
        return if (replyMsg != null && replyMsg.isTextMsg) replyMsg.content
        else messagesStateFlow.value.firstOrNull { !it.msg.fromSelf && it.msg.isTextMsg }?.msg?.content.orEmpty()
    }

    fun sendTextMsg(context: Context, isAIReply: Boolean = false, gotoBuy: () -> Unit) {
        App.launch {
            val userId = Membership.getUserId() ?: return@launch
            if (isAIReply) {
                aiReplyState = aiReplyState.copy(enabled = false, isUsed = true)
            }
            if (!isVip) {
                if (!isCanSendMsg(context, gotoBuy)) return@launch
            }

            showWaitingMessage = true

            val text = inputText
            onTextInput("")

            val replyMsg = selectedReplyMsg
            selectedReplyMsg = null

            val sendMsgStatus = msgRecordRepo.sendTextMsg(
                userId = userId,
                roleId = route.roleId,
                sessionId = route.sessionId,
                text = text,
                replyId = replyMsg?.msgId ?: 0,
                reply = replyMsg?.content
            )

            if (sendMsgStatus == null) {
                showWaitingMessage = false
            }
        }
        UserManager.onSendMsgEvent()
    }

    private fun isCanSendMsg(context: Context, onGotoBuy: () -> Unit): Boolean {
        val count = messagesStateFlow.value.count { !it.msg.fromSelf }
        if (count >= 10) {
            RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_CHATS, onGotoBuy).show()
            return false
        }
        return true
    }

    fun isShowMsgSelectedPopup(msg: MsgRecord): Boolean {
        return longPressedMsg != null && longPressedMsg?.msgId == msg.msgId
    }

    fun isShowDeletePopup(msg: MsgRecord): Boolean {
        return selectedVideoMsg != null && selectedVideoMsg?.msgId == msg.msgId
    }

    fun showLongPressedMsgPopup(msg: MsgRecord?) {
        longPressedMsg = msg
    }

    fun showDeleteVideoPop(msg: MsgRecord?) {
        selectedVideoMsg = msg
    }

    fun cancelReply() {
        selectedReplyMsg = null
    }

    fun onReplyMsg() {
        selectedReplyMsg = longPressedMsg
    }

    fun onDeleteMsg(deleteMsg: MsgRecord?) {
        viewModelScope.launch {
            deleteMsg ?: return@launch
            deleteMsg.msgId ?: return@launch
            msgRecordRepo.deleteMsg(deleteMsg.userId, deleteMsg.roleId, deleteMsg.msgId)
        }
    }

    suspend fun saveByteArrayToFile(byteArray: ByteArray, fileName: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                // 1. 获取应用私有目录（无需权限）
                val parentPath = (App.getInstance().filesDir.path + "/voice").toPath().toFile()
                if (!parentPath.exists()) {
                    parentPath.mkdirs()
                }
                val file = File(parentPath, fileName)
                // 2. 将 ByteArray 写入文件
                FileOutputStream(file).use { fos ->
                    fos.write(byteArray)
                    fos.flush()
                }
                // 可选：提示保存路径
                logd("文件保存成功: ${file.absolutePath}")
                file.absolutePath
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }
    }

    fun isTTSPlaying(msg: MsgRecord): Boolean {
        return playingVoiceMsgRecord != null && playingVoiceMsgRecord?.msgId == msg.msgId
    }

    fun text2Audio(
        context: Context,
        msgRecord: MsgRecord,
        voiceoverId: String,
        gotoBuy: () -> Unit
    ) {
        viewModelScope.launch {
            try {
                if (playingVoiceMsgRecord == msgRecord) {
                    stopTTSPlay()
                    return@launch
                }
                playingVoiceMsgRecord = msgRecord
                // 说明之前已经缓存过了
                if (msgRecord.voicePath != null) {
                    logv("播放本地语音 path = ${msgRecord.voicePath}")
                    player.playUrl(msgRecord.voicePath)
                    return@launch
                }
                val count = UserManager.getTtsTrialCount()
                if (!Membership.isVip() && UserManager.getTtsTrialCount() >= 3) {
                    RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_VOICE, gotoBuy).show()
                    return@launch
                }
                val dataBody = Api.withOther(DeepTalkieApi::class.java).audioWithTimestamp(
                    msgRecord.content.trim(),
                    voiceoverId,
                    "0",
                    System.currentTimeMillis() / 1000
                )
                val byteArray = Base64.decode(dataBody.data!!.audio_base64, Base64.DEFAULT)
                logv("tts语音下载成功 size = ${byteArray.size}")
                val path = saveByteArrayToFile(
                    byteArray,
                    "${aiRole?.id}_${System.currentTimeMillis()}.mp3"
                )
                if (path == null) {
                    showToast("play audio failed")
                    playingVoiceMsgRecord = null
                    return@launch
                }
                chatsRepo.setVoicePath(msgRecord.id, path)
                player.playUrl(path)
                if (!Membership.isVip()) {
                    UserManager.setTtsTrialCount(count + 1)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                loge("播放出现异常: ${e.stackTraceToString()}")
                showToast("play audio failed")
                playingVoiceMsgRecord = null
            }
        }
    }

    fun favorite(roleId: Long, favorite: Int) {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            chatsRepo.favoriteFun(userId, roleId, favorite)
        }
    }

    fun startReportActivity(context: Context, roleName: String) {
        val intent = Intent(context, ReportActivity::class.java)
        intent.putExtra("role_name", roleName)
        context.startActivity(intent)
    }

    fun stopTTSPlay() {
        playingVoiceMsgRecord = null
        player.pause()
        player.stop()
    }

    override fun onCleared() {
        super.onCleared()
        player.clearMediaItems()
        player.release()
    }
}

@Stable
data class AiReplyState(
    val enabled: Boolean = false,
    val aiReplyLastCount: Int = UserManager.getAIReplyLastCount(),
    val isUsed: Boolean = true,
    val messages: List<String?> = emptyList()
)