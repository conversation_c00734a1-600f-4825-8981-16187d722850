package com.deeptalkie.main.compose.ui.page.roledetail

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.clevguard.utils.ext.loge
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.theme.Black50
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.ext.imageRequest
import com.deeptalkie.main.view.RequirePurchaseDialog
import com.skydoves.landscapist.coil3.CoilImage
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.transformation.blur.BlurTransformationPlugin
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/6/18 11:50
 */

@Composable
fun RoleDetailRoute(
    onBack: () -> Unit,
    onLogin: () -> Unit,
    onBuy: () -> Unit,
    onChatClick: ((roleId: Long, sessionId: Long) -> Unit),
    onReport: (String) -> Unit
) {
    RoleDetailScreen(
        onBack = onBack,
        onLogin = onLogin,
        onChatClick = onChatClick,
        onReport = onReport,
        onBuy = onBuy
    )
}


@Composable
private fun RoleDetailScreen(
    onBack: () -> Unit,
    onReport: (String) -> Unit,
    onLogin: () -> Unit,
    onBuy: () -> Unit,
    onChatClick: ((roleId: Long, sessionId: Long) -> Unit),
    viewModel: DTRoleDetailViewModel = viewModel()
) {
    val memberBean by viewModel.memberFlow.collectAsState(null)
    val isVip by Membership.vipStateFlow.collectAsState(false)
    val isLogin by remember { derivedStateOf { memberBean != null } }
    val scope = rememberCoroutineScope()
    val navigationHeightDp = with(LocalDensity.current) {
        val navigationHeight by rememberUpdatedState(WindowInsets.navigationBars.getBottom(this))
        navigationHeight.toDp()
    }

    DTPage(background = colorResource(R.color.color_0D1116), loading = viewModel.loading) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = navigationHeightDp + 82.dp)
        ) {
            item {
                RoleImage(viewModel, isVip, onBuy)
            }
            item {
                LayRoleInfo(viewModel)
            }
        }
        val context = LocalContext.current
        LayBottom(onChat = {
            if (memberBean == null) {
                onLogin()
            } else {
                scope.launch {
                    viewModel.roleDetail?.let {
                        val sessionId = viewModel.startChat(
                            it.id, context,
                            gotoLogin = onLogin,
                            gotoBuy = onBuy
                        ) ?: return@launch
                        onChatClick(it.id, sessionId)
                    }
                }
            }
        })
        TopBar(
            onBack = onBack,
            isLogin = isLogin,
            onLogin = onLogin,
            onReport = onReport,
            viewModel = viewModel
        )
    }
}

@Composable
private fun LayRoleInfo(viewModel: DTRoleDetailViewModel) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp)
    ) {
        Text(
            viewModel.roleDetail?.name ?: "",
            style = TextStyle(
                fontSize = 20.sp,
                color = White,
                fontWeight = FontWeight.W700
            )
        )
        DTVerticalSpacer(16.dp)
        Text(
            viewModel.roleDetail?.description ?: "",
            style = TextStyle(
                fontSize = 13.sp,
                color = Color(0xffECEBED),
                lineHeight = 18.sp
            ), modifier = Modifier.fillMaxWidth()
        )
        DTVerticalSpacer(16.dp)
        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 0.5.dp,
            color = Color(0xff323436)
        )
        DTVerticalSpacer(16.dp)
        FlowRow(
            modifier = Modifier
                .padding(horizontal = 10.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(5.dp),
            verticalArrangement = Arrangement.spacedBy(5.dp),
        ) {
            viewModel.roleDetail?.tags?.forEach { tag ->
                ItemTag(tag.name)
            }
        }
    }
}

@Composable
private fun RoleImage(viewModel: DTRoleDetailViewModel, isVip: Boolean, onBuy: () -> Unit) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(500.dp)
        )
        AsyncImage(
            viewModel.currentPreviewImage,
            contentDescription = "img",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Crop
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(145.dp)
                .background(
                    Brush.verticalGradient(
                        listOf(Color(0x00000000), Black50, Black100)
                    )
                )
                .align(Alignment.BottomCenter)
        )
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .padding(bottom = 16.dp)
                .align(Alignment.BottomCenter)
        ) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(5.dp),
            ) {
                itemsIndexed(
                    viewModel.roleDetail?.images ?: emptyList(),
                    key = { _, url -> url }
                ) { index, url ->
                    ItemImage(index, isVip, url, onPreview = {
                        viewModel.currentPreviewImage = it ?: ""
                    }, gotoBuy = onBuy)
                }
            }
        }
    }
}

@Composable
private fun BoxScope.TopBar(
    viewModel: DTRoleDetailViewModel,
    onBack: () -> Unit,
    isLogin: Boolean,
    onLogin: () -> Unit,
    onReport: (String) -> Unit,
) {
    Row(
        modifier = Modifier
            .align(alignment = Alignment.TopCenter)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(56.dp)
            .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(R.drawable.ic_back2),
            contentDescription = "backIcon",
            modifier = Modifier
                .click(onBack)
                .size(36.dp)
        )
        Spacer(modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(if (viewModel.roleDetail?.isFavorite == 0) R.drawable.ic_uncollected else R.drawable.ic_collected),
            contentDescription = "backIcon",
            modifier = Modifier
                .click {
                    if (isLogin) {
                        loge("数据${viewModel.roleDetail?.isFavorite}")
                        val role = viewModel.roleDetail ?: return@click
                        val favoriteState = if (role.isFavorite == 0) {
                            1
                        } else {
                            0
                        }
                        viewModel.favorite(role.id, favoriteState)
                        viewModel.roleDetail =
                            viewModel.roleDetail?.copy(isFavorite = favoriteState)
                        loge("数据${viewModel.roleDetail?.isFavorite}")
                    } else {
                        onLogin()
                    }
                }
                .size(36.dp)
        )
        DTHorizontalSpacer(10.dp)
        Image(
            painter = painterResource(R.drawable.ic_report),
            contentDescription = "backIcon",
            modifier = Modifier
                .click {
                    onReport(viewModel.roleDetail?.name ?: "")
                }
                .size(36.dp)
        )
    }
}

@Composable
private fun BoxScope.LayBottom(onChat: () -> Unit) {
    Box(
        Modifier
            .fillMaxWidth()
            .background(Black100)
            .navigationBarsPadding()
            .padding(vertical = 18.dp)
            .align(Alignment.BottomCenter)
    ) {
        DTButton(
            R.string.chat,
            modifier = Modifier
                .padding(horizontal = 30.dp)
                .fillMaxWidth()
                .height(46.dp),
            contentColor = White,
            containerColor = SocialBlue,
            onClick = onChat
        )
    }
}

@Composable
private fun ItemImage(
    index: Int,
    isVip: Boolean,
    url: String,
    onPreview: (url: String?) -> Unit,
    gotoBuy: () -> Unit
) {
    val context = LocalContext.current
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.click {
            if (isVip) {
                onPreview(url)
            } else {
                if (index == 0) return@click
                RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_PICTURE, gotoBuy).show()
            }
        }
    ) {
        CoilImage(
            imageRequest = {
                url.imageRequest(context)
            },
            Modifier
                .size(50.dp)
                .clip(RoundedCornerShape(12.dp)),
            component = if (index == 0) {
                rememberImageComponent {}
            } else {
                if (isVip) {
                    rememberImageComponent {}
                } else {
                    rememberImageComponent {
                        +BlurTransformationPlugin(radius = 80)
                    }
                }
            }
        )
        if (!isVip) {
            if (index != 0) {
                Image(
                    painterResource(R.drawable.ic_locked),
                    contentDescription = "lockIcon",
                    modifier = Modifier
                        .size(24.dp),
                    contentScale = ContentScale.Crop
                )
            }
        }
    }
}

@Composable
private fun ItemTag(tag: String) {
    Row(
        modifier = Modifier
            .background(White10, RoundedCornerShape(100.dp))
            .wrapContentSize()
            .padding(horizontal = 7.dp, 4.dp)
    ) {
        Text(
            text = tag,
            style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W500, color = White)
        )
    }
}

